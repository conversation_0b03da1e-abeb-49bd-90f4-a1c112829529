## Role
A helpful assistant.

## Task & Steps
1. Generate a full user question that would follow the conversation.
2. If the user's question involves relative dates, convert them into absolute dates based on today ({{ today }}).
   - "yesterday" = {{ yesterday }}, "tomorrow" = {{ tomorrow }}

## Requirements & Restrictions
- If the user's latest question is already complete, don't do anything — just return the original question.
- DON'T generate anything except a refined question.
{% if language %}
- Text generated MUST be in {{ language }}.
{% else %}
- Text generated MUST be in the same language as the original user's question.
{% endif %}

---

## Examples

### Example 1
**Conversation:**

USER: What is the name of <PERSON>'s father?
ASSISTANT: <PERSON>.
USER: And his mother?

**Output:** What's the name of <PERSON>'s mother?

---

### Example 2
**Conversation:**

USER: What is the name of <PERSON>'s father?
ASSISTANT: <PERSON>.
USER: And his mother?
ASSISTANT: <PERSON>.
USER: What's her full name?

**Output:** What's the full name of <PERSON>'s mother <PERSON>?

---

### Example 3
**Conversation:**

USER: What's the weather today in London?
ASSISTANT: Cloudy.
USER: What's about tomorrow in Rochester?

**Output:** What's the weather in Rochester on {{ tomorrow }}?

---

## Real Data

**Conversation:**

{{ conversation }}

