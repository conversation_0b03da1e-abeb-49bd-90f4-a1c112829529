#!/usr/bin/env python3
"""
调试脚本：验证参数传递和修改
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mcp.server.server import RAGFlowConnector


class DebugRAGFlowConnector(RAGFlowConnector):
    """调试版本的 RAGFlowConnector，打印所有参数"""
    
    def retrieval(
        self, dataset_ids, document_ids=None, question="", page=1, page_size=6, 
        similarity_threshold=0.4, vector_similarity_weight=0.4, top_k=6, 
        rerank_id: str | None = None, keyword: bool = True
    ):
        print("\n" + "=" * 50)
        print("🔍 RAGFlowConnector.retrieval 被调用")
        print("=" * 50)
        print(f"📝 参数详情:")
        print(f"   dataset_ids: {dataset_ids}")
        print(f"   document_ids: {document_ids}")
        print(f"   question: '{question}'")
        print(f"   page: {page}")
        print(f"   page_size: {page_size}")
        print(f"   similarity_threshold: {similarity_threshold}")
        print(f"   vector_similarity_weight: {vector_similarity_weight}")
        print(f"   top_k: {top_k}")
        print(f"   rerank_id: {rerank_id}")
        print(f"   keyword: {keyword}")
        print("=" * 50)
        
        # 构建请求数据
        if document_ids is None:
            document_ids = []
        data_json = {
            "page": page,
            "page_size": page_size,
            "similarity_threshold": similarity_threshold,
            "vector_similarity_weight": vector_similarity_weight,
            "top_k": top_k,
            "rerank_id": rerank_id,
            "keyword": keyword,
            "question": question,
            "dataset_ids": dataset_ids,
            "document_ids": document_ids,
        }
        
        print(f"📡 将要发送的 JSON 数据:")
        import json
        print(json.dumps(data_json, indent=2, ensure_ascii=False))
        print("=" * 50)
        
        # 模拟返回结果（避免实际 API 调用）
        print("⚠️  注意：这是模拟结果，未进行实际 API 调用")
        
        # 根据 page_size 返回模拟数据
        mock_chunks = []
        for i in range(min(page_size, 10)):  # 最多返回10个模拟结果
            mock_chunks.append({
                "chunk_id": f"chunk_{i+1}",
                "content": f"这是第 {i+1} 个模拟检索结果",
                "similarity_score": 0.8 - i * 0.05
            })
        
        print(f"📊 模拟返回 {len(mock_chunks)} 个结果")
        return mock_chunks


def test_parameter_passing():
    """测试参数传递"""
    
    print("🚀 开始测试参数传递...")
    
    # 创建调试连接器
    connector = DebugRAGFlowConnector(base_url="http://127.0.0.1:9380")
    
    print("\n📋 测试1: 使用默认参数")
    result1 = connector.retrieval(
        dataset_ids=["test-dataset"],
        question="测试问题1"
    )
    print(f"✅ 返回结果数量: {len(result1)}")
    
    print("\n📋 测试2: 自定义参数")
    result2 = connector.retrieval(
        dataset_ids=["test-dataset"],
        question="测试问题2",
        page_size=3,
        similarity_threshold=0.5,
        top_k=3
    )
    print(f"✅ 返回结果数量: {len(result2)}")
    
    print("\n📋 测试3: 验证 call_tool 函数行为")
    
    # 模拟 call_tool 函数的参数字典
    arguments = {
        "dataset_ids": ["test-dataset"],
        "question": "测试问题3",
        "page_size": 2,  # 这个参数现在应该被传递
        "top_k": 2
    }
    
    print(f"🔧 模拟 call_tool 传入的 arguments: {arguments}")
    
    # 模拟修改后的 call_tool 调用
    result3 = connector.retrieval(
        dataset_ids=arguments["dataset_ids"],
        document_ids=arguments.get("document_ids", []),
        question=arguments["question"],
        page=arguments.get("page", 1),
        page_size=arguments.get("page_size", 6),  # 默认值6
        similarity_threshold=arguments.get("similarity_threshold", 0.4),  # 默认值0.4
        vector_similarity_weight=arguments.get("vector_similarity_weight", 0.4),  # 默认值0.4
        top_k=arguments.get("top_k", 6),  # 默认值6
        rerank_id=arguments.get("rerank_id"),
        keyword=arguments.get("keyword", True),  # 默认值True
    )
    print(f"✅ 返回结果数量: {len(result3)}")


def check_function_signature():
    """检查函数签名"""
    
    print("\n" + "=" * 50)
    print("🔍 检查函数签名")
    print("=" * 50)
    
    import inspect
    
    # 检查原始 RAGFlowConnector
    original_sig = inspect.signature(RAGFlowConnector.retrieval)
    print("📝 原始 RAGFlowConnector.retrieval 签名:")
    print(f"   {original_sig}")
    
    print("\n📋 参数详情:")
    for param_name, param in original_sig.parameters.items():
        if param.default != inspect.Parameter.empty:
            print(f"   {param_name}: 默认值 = {param.default}")
        else:
            print(f"   {param_name}: (必需参数)")
    
    # 检查调试版本
    debug_sig = inspect.signature(DebugRAGFlowConnector.retrieval)
    print(f"\n📝 调试版本 DebugRAGFlowConnector.retrieval 签名:")
    print(f"   {debug_sig}")


if __name__ == "__main__":
    print("🔧 RAGFlow MCP 参数调试工具")
    print("=" * 60)
    
    # 检查函数签名
    check_function_signature()
    
    # 测试参数传递
    test_parameter_passing()
    
    print("\n" + "=" * 60)
    print("📋 调试总结")
    print("=" * 60)
    print("1. ✅ 已修改 call_tool 函数传递所有参数")
    print("2. ✅ 默认参数值已更新为你的设置")
    print("3. 🔄 重启 RAGFlow MCP 服务后应该生效")
    print("4. 📊 观察实际返回结果数量是否符合 page_size")
    print("\n💡 下一步:")
    print("   - 重启 RAGFlow MCP 服务")
    print("   - 使用实际数据集测试")
    print("   - 观察返回结果数量变化")
