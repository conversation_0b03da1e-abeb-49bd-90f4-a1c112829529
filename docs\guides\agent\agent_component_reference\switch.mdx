---
sidebar_position: 6
slug: /switch_component
---

# Switch component

A component that evaluates whether specified conditions are met and directs the follow of execution accordingly. 

---

A **Switch** component evaluates conditions based on the output of specific components, directing the flow of execution accordingly to enable complex branching logic.

## Scenarios

A **Switch** component is essential for condition-based direction of execution flow. While it shares similarities with the [Categorize](./categorize.mdx) component, which is also used in multi-pronged strategies, the key distinction lies in their approach: the evaluation of the **Switch** component is rule-based, whereas the **Categorize** component involves AI and uses an LLM for decision-making. 

## Configurations

### Case n

A **Switch** component must have at least one case, each with multiple specified conditions. When multiple conditions are specified for a case, you must set the logical relationship between them to either AND or OR.

Once a new case is added, navigate to the **Switch** component on the canvas, find the **+** button next to the case, and click it to specify the downstream component(s).


#### Condition

Evaluates whether the output of specific components meets certain conditions

:::danger IMPORTANT
When you have added multiple conditions for a specific case, a **Logical operator** field appears, requiring you to set the logical relationship between these conditions as either AND or OR.
:::

- **Operator**: The operator required to form a conditional expression.
  - Equals (default)
  - Not equal
  - Greater than
  - Greater equal
  - Less than
  - Less equal
  - Contains 
  - Not contains 
  - Starts with
  - Ends with
  - Is empty
  - Not empty
- **Value**: A single value, which can be an integer, float, or string.  
  - Delimiters, multiple values, or expressions are *not* supported.

