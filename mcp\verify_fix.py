#!/usr/bin/env python3
"""
验证脚本：确认 RAGFlow MCP 参数修改已生效
"""

import json
import asyncio
import sys
import os
from typing import Dict, Any

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
server_dir = os.path.join(current_dir, 'server')
sys.path.insert(0, current_dir)
sys.path.insert(0, server_dir)

from server import RAGFlowConnector


class MockConnector(RAGFlowConnector):
    """模拟连接器，用于测试参数传递"""
    
    def __init__(self, base_url: str, version="v1"):
        super().__init__(base_url, version)
        self.last_call_params = None
    
    def retrieval(self, dataset_ids, document_ids=None, question="", page=1, page_size=6, 
                  similarity_threshold=0.4, vector_similarity_weight=0.4, top_k=6, 
                  rerank_id: str | None = None, keyword: bool = True):
        
        # 记录调用参数
        self.last_call_params = {
            "dataset_ids": dataset_ids,
            "document_ids": document_ids,
            "question": question,
            "page": page,
            "page_size": page_size,
            "similarity_threshold": similarity_threshold,
            "vector_similarity_weight": vector_similarity_weight,
            "top_k": top_k,
            "rerank_id": rerank_id,
            "keyword": keyword
        }
        
        print(f"🔍 retrieval 被调用，参数:")
        for key, value in self.last_call_params.items():
            print(f"   {key}: {value}")
        
        # 根据 page_size 返回模拟结果
        mock_results = []
        for i in range(page_size):
            mock_results.append({
                "chunk_id": f"chunk_{i+1}",
                "content": f"模拟内容 {i+1}",
                "score": 0.9 - i * 0.1
            })
        
        print(f"📊 返回 {len(mock_results)} 个模拟结果")
        return mock_results


def test_parameter_defaults():
    """测试默认参数"""
    
    print("=" * 60)
    print("🧪 测试1: 验证默认参数")
    print("=" * 60)
    
    connector = MockConnector("http://127.0.0.1:9380")
    
    # 只传递必需参数
    result = connector.retrieval(
        dataset_ids=["test-dataset"],
        question="测试问题"
    )
    
    expected_defaults = {
        "page": 1,
        "page_size": 6,
        "similarity_threshold": 0.4,
        "vector_similarity_weight": 0.4,
        "top_k": 6,
        "keyword": True
    }
    
    print(f"\n✅ 验证默认参数:")
    all_correct = True
    for key, expected_value in expected_defaults.items():
        actual_value = connector.last_call_params[key]
        if actual_value == expected_value:
            print(f"   ✅ {key}: {actual_value} (正确)")
        else:
            print(f"   ❌ {key}: {actual_value}, 期望: {expected_value}")
            all_correct = False
    
    if all_correct:
        print(f"\n🎉 所有默认参数都正确!")
        print(f"📊 返回结果数量: {len(result)} (应该等于 page_size=6)")
    else:
        print(f"\n⚠️  部分默认参数不正确")
    
    return all_correct


def test_call_tool_integration():
    """测试 call_tool 函数集成"""
    
    print("\n" + "=" * 60)
    print("🧪 测试2: 验证 call_tool 函数参数传递")
    print("=" * 60)
    
    # 模拟 MCP 客户端传入的参数
    test_cases = [
        {
            "name": "仅必需参数",
            "arguments": {
                "dataset_ids": ["dataset1"],
                "question": "什么是AI?"
            },
            "expected_page_size": 6  # 应该使用默认值
        },
        {
            "name": "自定义 page_size",
            "arguments": {
                "dataset_ids": ["dataset1"],
                "question": "什么是ML?",
                "page_size": 3
            },
            "expected_page_size": 3
        },
        {
            "name": "自定义所有参数",
            "arguments": {
                "dataset_ids": ["dataset1"],
                "question": "什么是DL?",
                "page": 2,
                "page_size": 2,
                "similarity_threshold": 0.5,
                "vector_similarity_weight": 0.6,
                "top_k": 2,
                "keyword": False
            },
            "expected_page_size": 2
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 子测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        # 创建新的模拟连接器
        mock_connector = MockConnector("http://127.0.0.1:9380")
        
        # 模拟 call_tool 的逻辑
        arguments = test_case["arguments"]
        
        result = mock_connector.retrieval(
            dataset_ids=arguments["dataset_ids"],
            document_ids=arguments.get("document_ids", []),
            question=arguments["question"],
            page=arguments.get("page", 1),
            page_size=arguments.get("page_size", 6),
            similarity_threshold=arguments.get("similarity_threshold", 0.4),
            vector_similarity_weight=arguments.get("vector_similarity_weight", 0.4),
            top_k=arguments.get("top_k", 6),
            rerank_id=arguments.get("rerank_id"),
            keyword=arguments.get("keyword", True),
        )
        
        # 验证结果
        actual_page_size = len(result)
        expected_page_size = test_case["expected_page_size"]
        
        if actual_page_size == expected_page_size:
            print(f"✅ 结果数量正确: {actual_page_size}")
        else:
            print(f"❌ 结果数量错误: {actual_page_size}, 期望: {expected_page_size}")


def check_modifications():
    """检查代码修改"""
    
    print("\n" + "=" * 60)
    print("🔍 检查代码修改")
    print("=" * 60)
    
    import inspect
    
    # 检查 RAGFlowConnector.retrieval 的签名
    sig = inspect.signature(RAGFlowConnector.retrieval)
    print(f"📝 RAGFlowConnector.retrieval 签名:")
    print(f"   {sig}")
    
    # 检查默认值
    print(f"\n📋 默认参数值:")
    expected_defaults = {
        "page": 1,
        "page_size": 6,
        "similarity_threshold": 0.4,
        "vector_similarity_weight": 0.4,
        "top_k": 6,
        "keyword": True
    }
    
    for param_name, param in sig.parameters.items():
        if param_name in expected_defaults:
            expected = expected_defaults[param_name]
            actual = param.default
            if actual == expected:
                print(f"   ✅ {param_name}: {actual}")
            else:
                print(f"   ❌ {param_name}: {actual}, 期望: {expected}")


def generate_usage_example():
    """生成使用示例"""
    
    print("\n" + "=" * 60)
    print("📖 使用示例")
    print("=" * 60)
    
    example_code = '''
# MCP 客户端调用示例

# 1. 使用默认参数 (page_size=6, similarity_threshold=0.4, etc.)
{
    "dataset_ids": ["your-dataset-id"],
    "question": "你的问题"
}

# 2. 自定义参数
{
    "dataset_ids": ["your-dataset-id"],
    "question": "你的问题",
    "page_size": 3,
    "similarity_threshold": 0.5,
    "top_k": 3
}

# 3. 完整参数
{
    "dataset_ids": ["your-dataset-id"],
    "question": "你的问题",
    "page": 1,
    "page_size": 6,
    "similarity_threshold": 0.4,
    "vector_similarity_weight": 0.4,
    "top_k": 6,
    "rerank_id": null,
    "keyword": true
}
'''
    
    print(example_code)


if __name__ == "__main__":
    print("🔧 RAGFlow MCP 参数修改验证工具")
    print("=" * 60)
    
    # 检查修改
    check_modifications()
    
    # 测试默认参数
    defaults_ok = test_parameter_defaults()
    
    # 测试 call_tool 集成
    test_call_tool_integration()
    
    # 生成使用示例
    generate_usage_example()
    
    print("\n" + "=" * 60)
    print("📋 验证总结")
    print("=" * 60)
    
    if defaults_ok:
        print("✅ 默认参数修改正确")
    else:
        print("❌ 默认参数修改有问题")
    
    print("✅ call_tool 函数已修改为传递所有参数")
    print("✅ 测试脚本运行完成")
    
    print("\n💡 下一步操作:")
    print("1. 重启 RAGFlow MCP 服务")
    print("2. 使用真实的数据集ID和API密钥测试")
    print("3. 观察返回结果数量是否符合预期")
    print("4. 如果仍有问题，检查后端 RAGFlow 服务的日志")
