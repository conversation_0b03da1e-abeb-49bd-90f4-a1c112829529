{"components": {"begin": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "Hi there!"}}, "downstream": ["categorize:0"], "upstream": []}, "categorize:0": {"obj": {"component_name": "Categorize", "params": {"llm_id": "deepseek-chat", "category_description": {"product_related": {"description": "The question is about the product usage, appearance and how it works.", "examples": [], "to": ["retrieval:0"]}, "others": {"description": "The question is not about the product usage, appearance and how it works.", "examples": [], "to": ["message:0"]}}}}, "downstream": [], "upstream": ["begin"]}, "message:0": {"obj": {"component_name": "Message", "params": {"content": ["Sorry, I don't know. I'm an AI bot."]}}, "downstream": [], "upstream": ["categorize:0"]}, "retrieval:0": {"obj": {"component_name": "Retrieval", "params": {"similarity_threshold": 0.2, "keywords_similarity_weight": 0.3, "top_n": 6, "top_k": 1024, "rerank_id": "", "empty_response": "Nothing found in dataset", "kb_ids": ["1a3d1d7afb0611ef9866047c16ec874f"]}}, "downstream": ["generate:0"], "upstream": ["categorize:0"]}, "generate:0": {"obj": {"component_name": "Agent", "params": {"llm_id": "deepseek-chat", "sys_prompt": "You are an intelligent assistant. Please summarize the content of the knowledge base to answer the question. Please list the data in the knowledge base and answer in detail. When all knowledge base content is irrelevant to the question, your answer must include the sentence \"The answer you are looking for is not found in the knowledge base!\" Answers need to consider chat history.\n      Here is the knowledge base:\n      {retrieval:0@formalized_content}\n      The above is the knowledge base.", "temperature": 0.2}}, "downstream": ["message:1"], "upstream": ["retrieval:0"]}, "message:1": {"obj": {"component_name": "Message", "params": {"content": ["{generate:0@content}"]}}, "downstream": [], "upstream": ["generate:0"]}}, "history": [], "path": [], "retrival": {"chunks": [], "doc_aggs": []}, "globals": {"sys.query": "", "sys.user_id": "", "sys.conversation_turns": 0, "sys.files": []}}