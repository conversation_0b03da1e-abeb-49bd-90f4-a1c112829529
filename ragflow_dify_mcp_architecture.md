# RAGFlow + Dify + MCP 智能知识问答系统架构流程图

## 系统架构概述

本系统采用两阶段设计：离线知识处理与在线智能问答，通过 MCP 安全网关确保系统的安全性和可追溯性。

## 流程图

```mermaid
flowchart TD
    %% 阶段一：知识处理与入库 (离线流程)
    subgraph Stage1 [" 阶段一：知识处理与入库 (离线流程)"]
        direction TB
        A1[" 原始知识源<br/><br/> PDF法规文档<br/> 设备手册(含图表)<br/> 运维经验记录<br/>"] 
        
        subgraph RagflowPipeline [" Ragflow 高级文档处理流水线"]
            direction TB
            B1[" 布局感知解析<br/><br/> deepdoc 解析器<br/> 文本提取<br/> 表格识别<br/> 图片内容理解"]
            B2[" 战略性分块<br/><br/> 模板化分块策略"]
            B3[" 向量化与元数据丰富"]
        end
        
        C1[(" 向量数据库<br/>VectorDB<br/> 高效语义搜索")]
    end

    %% 阶段二：智能体检索与生成 (在线流程)
    subgraph Stage2 [" 阶段二：智能体检索与生成 (在线流程)"]
        direction TB
        D1[" 用户查询<br/><br/> 业务问题输入"]
        
        E1[" Dify 应用与工作流<br/><br/> 用户交互界面<br/> 可视化工作流构建<br/> Agent 编排控制"]
        
        subgraph MCPGateway [" MCP 安全网关 (核心集成点)"]
            direction TB
            F1[" 鉴权与授权<br/><br/> 验证调用方身份<br/> 权限检查"]
            F2[" 安全过滤<br/><br/> 输入清洗<br/> 防止提示词注入"]
            F3[" 全链路审计<br/><br/> 记录调用信息<br/> 操作可追溯"]
        end
        
        G1[" Ragflow 检索服务<br/><br/> 混合搜索执行<br/> 相关上下文返回<br/> API 接口调用"]
        
        H1[" LLM 生成与响应<br/><br/> 构建最终提示词<br/> 大语言模型生成<br/> 附带引用来源<br/> 高保真度回答"]
        
        I1[" 最终响应<br/><br/> 准确答案<br/> 来源引用<br/> 可追溯性保证"]
    end

    %% 流程连接 - 阶段一
    A1 ==> B1
    B1 ==> B2
    B2 ==> B3
    B3 ==> C1
    
    %% 流程连接 - 阶段二
    D1 ==> E1
    E1 ==> F1
    F1 ==> F2
    F2 ==> F3
    F3 ==> G1
    G1 <==> C1
    G1 ==> H1
    H1 ==> I1

    %% 样式定义
    classDef stageBox fill:#e3f2fd,stroke:#1565c0,stroke-width:3px,color:#0d47a1
    classDef sourceBox fill:#f1f8e9,stroke:#388e3c,stroke-width:2px,color:#1b5e20
    classDef processBox fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#880e4f
    classDef securityBox fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#e65100
    classDef dataBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#1b5e20
    classDef userBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#4a148c
    classDef llmBox fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#01579b
    classDef responseBox fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#2e7d32

    class Stage1,Stage2 stageBox
    class A1 sourceBox
    class RagflowPipeline,B1,B2,B3,G1 processBox
    class MCPGateway,F1,F2,F3 securityBox
    class C1 dataBox
    class D1 userBox
    class E1,H1 llmBox
    class I1 responseBox
```

## 系统特点

### 🔄 阶段一：知识处理与入库（离线流程）
- **原始知识源**：多格式文档支持，专业团队审核
- **RAGFlow 处理**：深度文档理解，保持信息完整性
- **向量存储**：高效检索基础设施

### ⚡ 阶段二：智能体检索与生成（在线流程）
- **Dify 工作流**：可视化 Agent 编排
- **MCP 安全网关**：三重安全保护机制
- **智能生成**：高保真度回答，完整引用链

### 🎯 核心优势
1. **安全性**：MCP 网关提供全方位安全保护
2. **可追溯性**：从知识源到答案的完整链路追踪
3. **高保真度**：RAGFlow 确保信息不失真
4. **模块化**：组件职责清晰，易于维护扩展

## 技术栈
- **知识处理**：RAGFlow + deepdoc 解析器
- **工作流编排**：Dify 平台
- **安全网关**：MCP (Model Context Protocol)
- **向量存储**：VectorDB
- **AI 生成**：大语言模型 (LLM)

## 使用说明
1. 在 VSCode 中安装 Mermaid Preview 插件
2. 打开此文件，右键选择 "Mermaid: Preview Diagram"
3. 或使用快捷键 `Ctrl+Shift+P` 搜索 "Mermaid Preview"
