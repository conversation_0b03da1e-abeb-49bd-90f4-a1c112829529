# RAGFlow + Dify + MCP 智能知识问答系统架构流程图

## 系统架构概述

本系统采用两阶段设计：离线知识处理与在线智能问答，通过 MCP 安全网关确保系统的安全性和可追溯性。

## 流程图

```mermaid
flowchart LR
    %% 离线流程
    A["原始知识源<br/>PDF/手册/经验"]
    B["RAGFlow 处理<br/>解析+分块+向量化"]
    C[("向量数据库<br/>VectorDB")]

    %% 在线流程
    D["用户查询"]
    E["Dify 工作流<br/>Agent 编排"]
    F["MCP 安全网关<br/>鉴权+过滤+审计"]
    G["RAGFlow 检索<br/>混合搜索"]
    H["LLM 生成<br/>智能回答"]
    I["最终响应<br/>答案+引用"]

    %% 流程连接
    A --> B --> C
    D --> E --> F --> G
    G <--> C
    G --> H --> I

    %% 样式定义
    classDef sourceBox fill:#f1f8e9,stroke:#388e3c,stroke-width:2px
    classDef processBox fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef securityBox fill:#fff8e1,stroke:#f57c00,stroke-width:2px
    classDef dataBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef userBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef llmBox fill:#e1f5fe,stroke:#0277bd,stroke-width:2px

    class A sourceBox
    class B,G processBox
    class F securityBox
    class C dataBox
    class D,I userBox
    class E,H llmBox
```

## 系统特点

### 🔄 阶段一：知识处理与入库（离线流程）
- **原始知识源**：多格式文档支持，专业团队审核
- **RAGFlow 处理**：深度文档理解，保持信息完整性
- **向量存储**：高效检索基础设施

### ⚡ 阶段二：智能体检索与生成（在线流程）
- **Dify 工作流**：可视化 Agent 编排
- **MCP 安全网关**：三重安全保护机制
- **智能生成**：高保真度回答，完整引用链

### 🎯 核心优势
1. **安全性**：MCP 网关提供全方位安全保护
2. **可追溯性**：从知识源到答案的完整链路追踪
3. **高保真度**：RAGFlow 确保信息不失真
4. **模块化**：组件职责清晰，易于维护扩展

## 技术栈
- **知识处理**：RAGFlow + deepdoc 解析器
- **工作流编排**：Dify 平台
- **安全网关**：MCP (Model Context Protocol)
- **向量存储**：VectorDB
- **AI 生成**：大语言模型 (LLM)

## 使用说明
1. 在 VSCode 中安装 Mermaid Preview 插件
2. 打开此文件，右键选择 "Mermaid: Preview Diagram"
3. 或使用快捷键 `Ctrl+Shift+P` 搜索 "Mermaid Preview"
