# RAGFlow + Dify + MCP 智能知识问答系统架构流程图

## 系统架构概述

本系统采用两阶段设计：离线知识处理与在线智能体决策执行，通过 MCP 安全网关作为统一安全中枢，支持知识检索和数据分析双重能力。

## 流程图

```mermaid
flowchart LR
    %% 阶段一：知识处理与入库 (离线流程)
    A["原始知识源<br/>PDF法规/设备手册/运维经验"]

    subgraph RagflowPipeline ["RAGFlow 高级文档处理流水线"]
        B1["布局感知解析<br/>deepdoc 解析器<br/>文本/表格/图片提取"]
        B2["战略性分块<br/>模板化分块策略<br/>保留上下文完整性"]
        B3["向量化与元数据丰富<br/>嵌入模型转换<br/>附加来源章节信息"]
    end

    C[("向量数据库<br/>VectorDB<br/>海量向量存储")]

    %% 阶段二：智能体决策与执行 (在线流程)
    D["用户查询<br/>法规问题/数据分析需求"]

    E["Dify Agent<br/>决策大脑<br/>ReAct/Function Calling<br/>工具选择与任务规划"]

    F["MCP 安全网关<br/>统一安全中枢<br/>鉴权+过滤"]

    subgraph BackendServices ["后端服务层"]
        G1["RAGFlow 检索服务<br/>混合搜索<br/>图文知识上下文"]
        G2["AI 服务基座<br/>算法服务<br/>国发数据识别/工单查询"]
    end

    H["LLM 生成与响应<br/>构建最终提示词<br/>生成人类可读回答"]

    I["最终响应<br/>知识回答/数据报告<br/>可追溯引用"]

    %% 流程连接 - 离线流程
    A --> B1 --> B2 --> B3 --> C

    %% 流程连接 - 在线流程
    D --> E
    E --> F
    F --> G1
    F --> G2
    G1 <--> C
    G1 --> H
    G2 --> H
    H --> I

    %% 样式定义
    classDef sourceBox fill:#f1f8e9,stroke:#388e3c,stroke-width:2px
    classDef processBox fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef securityBox fill:#fff8e1,stroke:#f57c00,stroke-width:2px
    classDef dataBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef userBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef llmBox fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef agentBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef serviceBox fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px

    class A sourceBox
    class B1,B2,B3 processBox
    class F securityBox
    class C dataBox
    class D,I userBox
    class E agentBox
    class G1,G2 serviceBox
    class H llmBox
```

## 系统特点

### 🔄 阶段一：知识处理与入库（离线流程）
- **原始知识源**：PDF法规、设备手册、运维经验，专业团队（马俊负责）审核
- **RAGFlow 处理流水线**：
  - **布局感知解析**：deepdoc 解析器，精准提取文本/表格/图片
  - **战略性分块**：模板化策略，保留上下文完整性
  - **向量化处理**：嵌入模型转换，丰富元数据标记
- **向量数据库**：海量向量存储，支持快速语义搜索

### ⚡ 阶段二：智能体决策与执行（在线流程）
- **Dify Agent**：决策大脑，支持 ReAct/Function Calling，智能工具选择
- **MCP 安全网关**：统一安全中枢，提供鉴权、过滤、审计三重保护
- **后端服务层**：
  - **RAGFlow 检索服务**：混合搜索，返回图文知识上下文
  - **AI 服务基座**：算法服务，国发数据识别、工单查询等
- **LLM 生成**：构建最终提示词，生成人类可读的回答或报告

### 🎯 核心优势
1. **智能决策**：Agent 根据问题类型自动选择合适工具
2. **双重能力**：同时支持知识检索和数据分析
3. **统一安全**：MCP 网关统一管控所有后端服务访问
4. **全链路追溯**：从用户问题到最终答案的完整记录
5. **高保真度**：RAGFlow 确保图文信息不失真

## 技术栈
- **知识处理**：RAGFlow + deepdoc 解析器
- **智能体平台**：Dify Agent (ReAct/Function Calling)
- **安全网关**：MCP (Model Context Protocol)
- **向量存储**：VectorDB
- **后端服务**：RAGFlow 检索服务 + AI 服务基座
- **AI 生成**：大语言模型 (LLM)

## 使用说明
1. 在 VSCode 中安装 Mermaid Preview 插件
2. 打开此文件，右键选择 "Mermaid: Preview Diagram"
3. 或使用快捷键 `Ctrl+Shift+P` 搜索 "Mermaid Preview"
