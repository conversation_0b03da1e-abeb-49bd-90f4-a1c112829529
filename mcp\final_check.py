#!/usr/bin/env python3
"""
最终检查脚本：验证所有修改是否正确
"""

import os
import re

def check_server_py():
    """检查 server.py 文件的关键修改"""
    
    print("=" * 60)
    print("🔍 检查 server.py 文件")
    print("=" * 60)
    
    server_path = os.path.join(os.path.dirname(__file__), 'server', 'server.py')
    
    try:
        with open(server_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查项目列表
        checks = [
            {
                "name": "retrieval 函数默认参数 - page_size=6",
                "pattern": r"page_size=6",
                "required": True
            },
            {
                "name": "retrieval 函数默认参数 - similarity_threshold=0.4",
                "pattern": r"similarity_threshold=0\.4",
                "required": True
            },
            {
                "name": "retrieval 函数默认参数 - vector_similarity_weight=0.4",
                "pattern": r"vector_similarity_weight=0\.4",
                "required": True
            },
            {
                "name": "retrieval 函数默认参数 - top_k=6",
                "pattern": r"top_k=6",
                "required": True
            },
            {
                "name": "retrieval 函数默认参数 - keyword=True",
                "pattern": r"keyword: bool = True",
                "required": True
            },
            {
                "name": "call_tool 传递 page_size 参数",
                "pattern": r'page_size=arguments\.get\("page_size", 6\)',
                "required": True
            },
            {
                "name": "call_tool 传递 similarity_threshold 参数",
                "pattern": r'similarity_threshold=arguments\.get\("similarity_threshold", 0\.4\)',
                "required": True
            },
            {
                "name": "call_tool 传递 top_k 参数",
                "pattern": r'top_k=arguments\.get\("top_k", 6\)',
                "required": True
            },
            {
                "name": "inputSchema 包含 page_size",
                "pattern": r'"page_size":\s*{[^}]*"default":\s*6',
                "required": True
            },
            {
                "name": "inputSchema 包含 similarity_threshold",
                "pattern": r'"similarity_threshold":\s*{[^}]*"default":\s*0\.4',
                "required": True
            },
            {
                "name": "inputSchema 包含 top_k",
                "pattern": r'"top_k":\s*{[^}]*"default":\s*6',
                "required": True
            },
            {
                "name": "调试日志 - retrieval 函数",
                "pattern": r"RAGFlowConnector\.retrieval called with:",
                "required": False
            },
            {
                "name": "调试日志 - call_tool 函数",
                "pattern": r"call_tool received arguments:",
                "required": False
            }
        ]
        
        print("📋 检查结果:")
        all_required_passed = True
        
        for check in checks:
            found = bool(re.search(check["pattern"], content, re.MULTILINE | re.DOTALL))
            
            if found:
                status = "✅"
            elif check["required"]:
                status = "❌"
                all_required_passed = False
            else:
                status = "⚠️ "
            
            print(f"   {status} {check['name']}")
        
        print(f"\n📊 总体状态:")
        if all_required_passed:
            print("   🎉 所有必需的修改都已完成!")
        else:
            print("   ❌ 部分必需的修改缺失")
        
        return all_required_passed
        
    except FileNotFoundError:
        print(f"❌ 找不到文件: {server_path}")
        return False
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")
        return False


def extract_key_functions():
    """提取关键函数的内容"""
    
    print("\n" + "=" * 60)
    print("📝 关键函数内容")
    print("=" * 60)
    
    server_path = os.path.join(os.path.dirname(__file__), 'server', 'server.py')
    
    try:
        with open(server_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找 retrieval 函数定义
        print("🔍 retrieval 函数签名:")
        for i, line in enumerate(lines):
            if "def retrieval(" in line:
                # 打印函数签名（可能跨多行）
                func_lines = []
                j = i
                while j < len(lines) and not lines[j].strip().endswith("):"):
                    func_lines.append(lines[j].rstrip())
                    j += 1
                if j < len(lines):
                    func_lines.append(lines[j].rstrip())
                
                for func_line in func_lines:
                    print(f"   {func_line}")
                break
        
        # 查找 call_tool 函数中的 retrieval 调用
        print(f"\n🔍 call_tool 函数中的 retrieval 调用:")
        in_call_tool = False
        in_retrieval_call = False
        
        for i, line in enumerate(lines):
            if "async def call_tool(" in line:
                in_call_tool = True
            elif in_call_tool and "return connector.retrieval(" in line:
                in_retrieval_call = True
                print(f"   {line.rstrip()}")
            elif in_retrieval_call and line.strip().startswith(")"):
                print(f"   {line.rstrip()}")
                break
            elif in_retrieval_call:
                print(f"   {line.rstrip()}")
        
    except Exception as e:
        print(f"❌ 提取函数内容时出错: {e}")


def generate_test_commands():
    """生成测试命令"""
    
    print("\n" + "=" * 60)
    print("🧪 测试命令")
    print("=" * 60)
    
    commands = """
📋 重启服务并测试:

1. 🔄 重启 RAGFlow MCP 服务:
   python mcp/server/server.py --mode=self-host --api-key=your-api-key

2. 🧪 运行诊断脚本:
   python mcp/debug_live_service.py

3. 📊 观察控制台输出:
   - 查看调试日志中的参数值
   - 确认 page_size=6, top_k=6 等参数是否正确传递

4. 🔍 如果仍有问题，检查:
   - RAGFlow 后端服务是否正常运行
   - API Key 是否正确
   - 数据集ID是否存在
   - 网络连接是否正常

5. 📝 移除调试日志:
   测试完成后，可以移除 retrieval 和 call_tool 函数中的 print 语句
"""
    
    print(commands)


def main():
    print("🔧 RAGFlow MCP 最终检查工具")
    print("=" * 60)
    
    # 检查代码修改
    code_ok = check_server_py()
    
    # 提取关键函数
    extract_key_functions()
    
    # 生成测试命令
    generate_test_commands()
    
    print("\n" + "=" * 60)
    print("📋 检查总结")
    print("=" * 60)
    
    if code_ok:
        print("✅ 所有代码修改都已正确完成")
        print("🔄 现在请重启 RAGFlow MCP 服务")
        print("📊 重启后观察控制台的调试日志输出")
        print("🎯 默认应该返回最多 6 条结果")
    else:
        print("❌ 代码修改不完整，请检查上述缺失项")
    
    print(f"\n💡 关键点:")
    print(f"1. 确保服务完全重启")
    print(f"2. 观察调试日志中的参数值")
    print(f"3. 验证返回结果数量是否为 6")
    print(f"4. 测试自定义 page_size 参数")


if __name__ == "__main__":
    main()
