#!/usr/bin/env python3
"""
测试脚本：验证 RAGFlow MCP 检索参数修改是否生效
"""

import json
import requests
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mcp.server.server import RAGFlowConnector


def test_retrieval_params():
    """测试检索参数是否按预期工作"""
    
    # 配置
    BASE_URL = "http://127.0.0.1:9380"
    API_KEY = "your-api-key-here"  # 请替换为实际的 API Key
    
    # 创建连接器
    connector = RAGFlowConnector(base_url=BASE_URL)
    connector.bind_api_key(API_KEY)
    
    # 测试数据
    test_cases = [
        {
            "name": "默认参数测试",
            "params": {
                "dataset_ids": ["test-dataset-id"],
                "question": "什么是人工智能？"
            },
            "expected": {
                "page_size": 6,
                "similarity_threshold": 0.4,
                "vector_similarity_weight": 0.4,
                "top_k": 6,
                "keyword": True
            }
        },
        {
            "name": "自定义参数测试", 
            "params": {
                "dataset_ids": ["test-dataset-id"],
                "question": "什么是机器学习？",
                "page_size": 3,
                "similarity_threshold": 0.5,
                "vector_similarity_weight": 0.6,
                "top_k": 3,
                "keyword": False
            },
            "expected": {
                "page_size": 3,
                "similarity_threshold": 0.5,
                "vector_similarity_weight": 0.6,
                "top_k": 3,
                "keyword": False
            }
        }
    ]
    
    print("=" * 60)
    print("RAGFlow MCP 检索参数测试")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # 调用检索函数
            result = connector.retrieval(**test_case["params"])
            
            # 检查结果
            if isinstance(result, list) and len(result) > 0:
                print(f"✅ 检索成功")
                print(f"📊 返回结果数量: {len(result)}")
                
                # 验证参数是否按预期传递
                expected = test_case["expected"]
                print(f"🎯 预期参数:")
                for key, value in expected.items():
                    print(f"   {key}: {value}")
                    
                # 如果返回结果数量与 page_size 匹配
                if len(result) <= expected["page_size"]:
                    print(f"✅ page_size 参数生效: 返回 {len(result)} <= {expected['page_size']}")
                else:
                    print(f"⚠️  page_size 参数可能未生效: 返回 {len(result)} > {expected['page_size']}")
                    
            else:
                print(f"❌ 检索失败或无结果")
                print(f"📄 返回内容: {result}")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            print(f"🔍 错误类型: {type(e).__name__}")


def test_direct_api_call():
    """直接测试 API 调用，绕过 MCP 层"""
    
    BASE_URL = "http://127.0.0.1:9380"
    API_KEY = "your-api-key-here"  # 请替换为实际的 API Key
    
    print("\n" + "=" * 60)
    print("直接 API 调用测试")
    print("=" * 60)
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    test_data = {
        "dataset_ids": ["test-dataset-id"],
        "question": "什么是深度学习？",
        "page": 1,
        "page_size": 6,
        "similarity_threshold": 0.4,
        "vector_similarity_weight": 0.4,
        "top_k": 6,
        "keyword": True
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/retrieval",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"📡 HTTP 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API 调用成功")
            print(f"📊 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ API 调用失败")
            print(f"📄 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ API 调用异常: {str(e)}")


def print_current_config():
    """打印当前配置"""
    
    print("\n" + "=" * 60)
    print("当前 RAGFlowConnector 配置")
    print("=" * 60)
    
    # 创建连接器实例
    connector = RAGFlowConnector(base_url="http://127.0.0.1:9380")
    
    # 通过反射获取 retrieval 方法的默认参数
    import inspect
    sig = inspect.signature(connector.retrieval)
    
    print("🔧 retrieval 方法默认参数:")
    for param_name, param in sig.parameters.items():
        if param.default != inspect.Parameter.empty:
            print(f"   {param_name}: {param.default}")
        else:
            print(f"   {param_name}: (必需参数)")


if __name__ == "__main__":
    print("🚀 开始测试 RAGFlow MCP 检索参数...")
    
    # 打印当前配置
    print_current_config()
    
    # 运行测试
    test_retrieval_params()
    
    # 直接 API 测试
    test_direct_api_call()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("\n💡 使用说明:")
    print("1. 请确保 RAGFlow 服务正在运行 (http://127.0.0.1:9380)")
    print("2. 请替换脚本中的 API_KEY 为实际值")
    print("3. 请替换 dataset_ids 为实际的数据集ID")
    print("4. 观察返回结果数量是否符合 page_size 设置")
