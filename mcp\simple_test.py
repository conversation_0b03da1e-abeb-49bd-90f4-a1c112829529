#!/usr/bin/env python3
"""
简化测试脚本：验证参数修复
"""

import json
import sys
import os
import inspect

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
server_dir = os.path.join(current_dir, 'server')
sys.path.insert(0, current_dir)
sys.path.insert(0, server_dir)

from server import RAGFlowConnector


def test_retrieval_signature():
    """测试 retrieval 函数签名"""
    
    print("=" * 60)
    print("🔍 测试: retrieval 函数签名验证")
    print("=" * 60)
    
    sig = inspect.signature(RAGFlowConnector.retrieval)
    print(f"📝 函数签名: {sig}")
    
    # 检查默认值
    expected_defaults = {
        "page": 1,
        "page_size": 6,
        "similarity_threshold": 0.4,
        "vector_similarity_weight": 0.4,
        "top_k": 6,
        "keyword": True
    }
    
    print(f"\n📋 参数检查:")
    all_correct = True
    
    for param_name, param in sig.parameters.items():
        if param_name in expected_defaults:
            expected = expected_defaults[param_name]
            actual = param.default
            if actual == expected:
                print(f"   ✅ {param_name}: {actual}")
            else:
                print(f"   ❌ {param_name}: {actual}, 期望: {expected}")
                all_correct = False
        elif param.default != inspect.Parameter.empty:
            print(f"   ℹ️  {param_name}: {param.default}")
        else:
            print(f"   📝 {param_name}: (必需参数)")
    
    return all_correct


def test_parameter_passing():
    """测试参数传递逻辑"""
    
    print("\n" + "=" * 60)
    print("🔍 测试: 参数传递逻辑")
    print("=" * 60)
    
    class TestConnector(RAGFlowConnector):
        def __init__(self):
            # 不调用父类初始化，避免网络依赖
            self.captured_params = None
        
        def retrieval(self, dataset_ids, document_ids=None, question="", page=1, page_size=6, 
                      similarity_threshold=0.4, vector_similarity_weight=0.4, top_k=6, 
                      rerank_id: str | None = None, keyword: bool = True):
            
            self.captured_params = {
                "dataset_ids": dataset_ids,
                "document_ids": document_ids,
                "question": question,
                "page": page,
                "page_size": page_size,
                "similarity_threshold": similarity_threshold,
                "vector_similarity_weight": vector_similarity_weight,
                "top_k": top_k,
                "rerank_id": rerank_id,
                "keyword": keyword
            }
            
            # 模拟返回指定数量的结果
            return [f"结果{i+1}" for i in range(page_size)]
    
    connector = TestConnector()
    
    # 测试用例
    test_cases = [
        {
            "name": "默认参数",
            "args": {
                "dataset_ids": ["test"],
                "question": "测试"
            },
            "expected_page_size": 6
        },
        {
            "name": "自定义 page_size",
            "args": {
                "dataset_ids": ["test"],
                "question": "测试",
                "page_size": 3
            },
            "expected_page_size": 3
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print("-" * 40)
        
        # 调用函数
        result = connector.retrieval(**test_case["args"])
        
        # 检查参数
        print(f"📥 接收到的参数:")
        for key, value in connector.captured_params.items():
            print(f"   {key}: {value}")
        
        # 检查结果数量
        expected_count = test_case["expected_page_size"]
        actual_count = len(result)
        
        print(f"\n📊 结果验证:")
        print(f"   期望数量: {expected_count}")
        print(f"   实际数量: {actual_count}")
        
        if actual_count == expected_count:
            print(f"   ✅ 通过")
        else:
            print(f"   ❌ 失败")
            all_passed = False
    
    return all_passed


def test_call_tool_simulation():
    """模拟 call_tool 函数行为"""
    
    print("\n" + "=" * 60)
    print("🔍 测试: call_tool 模拟")
    print("=" * 60)
    
    class MockConnector(RAGFlowConnector):
        def __init__(self):
            self.last_call = None
        
        def retrieval(self, dataset_ids, document_ids=None, question="", page=1, page_size=6, 
                      similarity_threshold=0.4, vector_similarity_weight=0.4, top_k=6, 
                      rerank_id: str | None = None, keyword: bool = True):
            
            self.last_call = {
                "dataset_ids": dataset_ids,
                "document_ids": document_ids,
                "question": question,
                "page": page,
                "page_size": page_size,
                "similarity_threshold": similarity_threshold,
                "vector_similarity_weight": vector_similarity_weight,
                "top_k": top_k,
                "rerank_id": rerank_id,
                "keyword": keyword
            }
            
            return [f"chunk_{i}" for i in range(page_size)]
    
    # 模拟 MCP 客户端传入的参数
    arguments = {
        "dataset_ids": ["dataset-123"],
        "question": "什么是AI？",
        "page_size": 4,
        "similarity_threshold": 0.5,
        "top_k": 4
    }
    
    print(f"📤 客户端传入参数:")
    for key, value in arguments.items():
        print(f"   {key}: {value}")
    
    # 模拟修改后的 call_tool 逻辑
    connector = MockConnector()
    
    document_ids = arguments.get("document_ids", [])
    result = connector.retrieval(
        dataset_ids=arguments["dataset_ids"],
        document_ids=document_ids,
        question=arguments["question"],
        page=arguments.get("page", 1),
        page_size=arguments.get("page_size", 6),
        similarity_threshold=arguments.get("similarity_threshold", 0.4),
        vector_similarity_weight=arguments.get("vector_similarity_weight", 0.4),
        top_k=arguments.get("top_k", 6),
        rerank_id=arguments.get("rerank_id"),
        keyword=arguments.get("keyword", True),
    )
    
    print(f"\n📥 retrieval 接收到的参数:")
    for key, value in connector.last_call.items():
        print(f"   {key}: {value}")
    
    print(f"\n📊 返回结果:")
    print(f"   结果数量: {len(result)}")
    print(f"   结果内容: {result}")
    
    # 验证关键参数
    success = True
    if connector.last_call["page_size"] != 4:
        print(f"❌ page_size 错误: {connector.last_call['page_size']}")
        success = False
    if connector.last_call["similarity_threshold"] != 0.5:
        print(f"❌ similarity_threshold 错误: {connector.last_call['similarity_threshold']}")
        success = False
    if len(result) != 4:
        print(f"❌ 结果数量错误: {len(result)}")
        success = False
    
    if success:
        print(f"✅ call_tool 模拟测试通过!")
    else:
        print(f"❌ call_tool 模拟测试失败!")
    
    return success


def check_source_code():
    """检查源代码修改"""
    
    print("\n" + "=" * 60)
    print("🔍 检查源代码修改")
    print("=" * 60)
    
    try:
        with open(os.path.join(current_dir, 'server', 'server.py'), 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改点
        checks = [
            ("retrieval 函数默认参数", "page_size=6" in content),
            ("retrieval 函数默认参数", "similarity_threshold=0.4" in content),
            ("retrieval 函数默认参数", "top_k=6" in content),
            ("call_tool 参数传递", "arguments.get(\"page_size\", 6)" in content),
            ("call_tool 参数传递", "arguments.get(\"similarity_threshold\", 0.4)" in content),
            ("call_tool 参数传递", "arguments.get(\"top_k\", 6)" in content),
        ]
        
        print("📋 源代码检查:")
        all_good = True
        for check_name, condition in checks:
            if condition:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ 无法读取源代码: {e}")
        return False


if __name__ == "__main__":
    print("🔧 RAGFlow MCP 简化验证测试")
    print("=" * 60)
    
    # 运行所有测试
    test1 = test_retrieval_signature()
    test2 = test_parameter_passing()
    test3 = test_call_tool_simulation()
    test4 = check_source_code()
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    results = [
        ("retrieval 函数签名", test1),
        ("参数传递逻辑", test2),
        ("call_tool 模拟", test3),
        ("源代码检查", test4)
    ]
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n{'🎉 所有测试通过!' if all_passed else '⚠️  部分测试失败'}")
    
    if all_passed:
        print("\n💡 下一步:")
        print("1. 重启 RAGFlow MCP 服务")
        print("2. 测试实际调用，应该返回最多 6 条结果")
        print("3. 可以通过传递 page_size 参数自定义返回数量")
    else:
        print("\n🔧 需要检查的问题:")
        print("1. 确认所有代码修改都已保存")
        print("2. 检查函数签名和默认值")
        print("3. 验证 call_tool 函数的参数传递")
