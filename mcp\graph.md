```
graph TD
    subgraph "阶段一：知识处理与入库 (离线流程)"
        direction TB
        A -->|1. 收集与治理| B(Ragflow 高级文档处理流水线);
        B -->|A. 布局感知解析 (deepdoc)<br/>精准提取文本、表格、图片| C(B. 战略性分块<br/>模板化分块，保留语义);
        C -->|C. 向量化与元数据丰富| D;
    end

    subgraph "阶段二：智能体检索与生成 (在线流程)"
        direction LR
        E[用户] <--> F{Dify 应用界面};
        F --> G;
        G -->|1. 用户提问| H(2. 调用“安全知识检索”工具);
        H --> I[MCP 安全网关];
        I -->|3. 鉴权/审计/转发| J;
        J -->|4. 混合搜索<br/>(关键词+语义)| D;
        D -->|5. 返回相关上下文<br/>(含图文信息)| J;
        J -->|6. 返回安全上下文| I;
        I -->|7. 返回上下文至Dify| G;
        G -->|8. LLM 提示词构建与生成| K[大语言模型 (LLM)];
        K -->|9. 生成精准、可溯源的回答| G;
        G -->|10. 格式化最终响应| F;
    end
    ```
    