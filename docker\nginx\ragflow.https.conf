server {
    listen 80;
    server_name your-ragflow-domain.com;
    return 301 https://$host$request_uri;
}



server {
    listen 443 ssl;
    server_name your-ragflow-domain.com;

    ssl_certificate /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;

    root /ragflow/web/dist;

    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    location ~ ^/(v1|api) {
        proxy_pass http://ragflow:9380;
        include proxy.conf;
    }


    location / {
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # Cache-Control: max-age~@~AExpires
    location ~ ^/static/(css|js|media)/ {
        expires 10y;
        access_log off;
    }
}
