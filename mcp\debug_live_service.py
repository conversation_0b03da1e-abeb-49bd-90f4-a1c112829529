#!/usr/bin/env python3
"""
实时诊断脚本：检查 RAGFlow MCP 服务的实际行为
"""

import json
import requests
import time
import sys
import os

def test_mcp_service_directly():
    """直接测试 MCP 服务"""
    
    print("=" * 60)
    print("🔍 直接测试 RAGFlow MCP 服务")
    print("=" * 60)
    
    # 配置 - 请根据实际情况修改
    MCP_HOST = "127.0.0.1"
    MCP_PORT = 9382
    API_KEY = "your-api-key-here"  # 请替换为实际的 API Key
    
    # 测试数据
    test_payload = {
        "dataset_ids": ["test-dataset-id"],  # 请替换为实际的数据集ID
        "question": "什么是人工智能？",
        "page_size": 3,  # 明确指定返回3条
        "similarity_threshold": 0.5,
        "top_k": 3
    }
    
    print(f"📡 MCP 服务地址: http://{MCP_HOST}:{MCP_PORT}")
    print(f"📤 测试载荷:")
    print(json.dumps(test_payload, indent=2, ensure_ascii=False))
    
    # 测试 MCP 工具调用
    try:
        # 构建 MCP 调用请求
        mcp_request = {
            "method": "tools/call",
            "params": {
                "name": "ragflow_retrieval",
                "arguments": test_payload
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }
        
        print(f"\n🚀 发送 MCP 请求...")
        
        # 发送请求到 MCP 服务
        response = requests.post(
            f"http://{MCP_HOST}:{MCP_PORT}/mcp",
            headers=headers,
            json=mcp_request,
            timeout=30
        )
        
        print(f"📊 HTTP 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ MCP 调用成功")
            print(f"📄 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 分析结果
            if "result" in result:
                content = result["result"]
                if isinstance(content, list) and len(content) > 0:
                    # 尝试解析返回的文本内容
                    text_content = content[0].get("text", "")
                    if text_content:
                        # 计算返回的块数量
                        chunks = text_content.split('\n')
                        chunk_count = len([c for c in chunks if c.strip()])
                        print(f"\n📊 结果分析:")
                        print(f"   返回的文本块数量: {chunk_count}")
                        print(f"   期望数量: {test_payload['page_size']}")
                        
                        if chunk_count <= test_payload['page_size']:
                            print(f"   ✅ 参数可能已生效!")
                        else:
                            print(f"   ⚠️  返回数量超过预期，参数可能未生效")
                        
                        # 显示前几个结果
                        print(f"\n📝 前3个结果预览:")
                        for i, chunk in enumerate(chunks[:3]):
                            if chunk.strip():
                                print(f"   {i+1}. {chunk[:100]}...")
            
        else:
            print(f"❌ MCP 调用失败")
            print(f"📄 错误响应: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到 MCP 服务")
        print(f"💡 请确保 RAGFlow MCP 服务正在运行在 {MCP_HOST}:{MCP_PORT}")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False
    
    return True


def test_ragflow_api_directly():
    """直接测试 RAGFlow API"""
    
    print("\n" + "=" * 60)
    print("🔍 直接测试 RAGFlow API")
    print("=" * 60)
    
    # 配置
    RAGFLOW_HOST = "127.0.0.1"
    RAGFLOW_PORT = 9380
    API_KEY = "your-api-key-here"  # 请替换为实际的 API Key
    
    test_payload = {
        "dataset_ids": ["test-dataset-id"],  # 请替换为实际的数据集ID
        "question": "什么是机器学习？",
        "page": 1,
        "page_size": 3,  # 明确指定返回3条
        "similarity_threshold": 0.5,
        "vector_similarity_weight": 0.4,
        "top_k": 3,
        "keyword": True
    }
    
    print(f"📡 RAGFlow API 地址: http://{RAGFLOW_HOST}:{RAGFLOW_PORT}")
    print(f"📤 测试载荷:")
    print(json.dumps(test_payload, indent=2, ensure_ascii=False))
    
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }
        
        print(f"\n🚀 发送 RAGFlow API 请求...")
        
        response = requests.post(
            f"http://{RAGFLOW_HOST}:{RAGFLOW_PORT}/api/v1/retrieval",
            headers=headers,
            json=test_payload,
            timeout=30
        )
        
        print(f"📊 HTTP 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ RAGFlow API 调用成功")
            
            if result.get("code") == 0:
                chunks = result.get("data", {}).get("chunks", [])
                print(f"\n📊 结果分析:")
                print(f"   返回的块数量: {len(chunks)}")
                print(f"   期望数量: {test_payload['page_size']}")
                
                if len(chunks) <= test_payload['page_size']:
                    print(f"   ✅ RAGFlow API 参数生效!")
                else:
                    print(f"   ⚠️  RAGFlow API 返回数量超过预期")
                
                # 显示参数传递情况
                print(f"\n📋 传递的参数:")
                for key in ["page_size", "similarity_threshold", "top_k"]:
                    print(f"   {key}: {test_payload[key]}")
                    
            else:
                print(f"❌ RAGFlow API 返回错误: {result.get('message')}")
        else:
            print(f"❌ RAGFlow API 调用失败")
            print(f"📄 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ RAGFlow API 请求异常: {str(e)}")
        return False
    
    return True


def check_service_status():
    """检查服务状态"""
    
    print("\n" + "=" * 60)
    print("🔍 检查服务状态")
    print("=" * 60)
    
    services = [
        ("RAGFlow MCP", "127.0.0.1", 9382),
        ("RAGFlow API", "127.0.0.1", 9380)
    ]
    
    for service_name, host, port in services:
        try:
            response = requests.get(f"http://{host}:{port}", timeout=5)
            print(f"✅ {service_name} ({host}:{port}) - 可访问")
        except requests.exceptions.ConnectionError:
            print(f"❌ {service_name} ({host}:{port}) - 无法连接")
        except Exception as e:
            print(f"⚠️  {service_name} ({host}:{port}) - 异常: {str(e)}")


def generate_debug_instructions():
    """生成调试说明"""
    
    print("\n" + "=" * 60)
    print("🔧 调试说明")
    print("=" * 60)
    
    instructions = """
📋 如果参数仍然不生效，请按以下步骤调试:

1. 🔄 确认服务重启
   - 完全停止 RAGFlow MCP 服务
   - 重新启动服务
   - 确认使用了修改后的代码

2. 📝 检查日志
   - 查看 RAGFlow MCP 服务的启动日志
   - 查看 RAGFlow 后端服务的日志
   - 寻找参数传递相关的日志

3. 🧪 测试步骤
   - 先用本脚本测试 RAGFlow API 直接调用
   - 再测试 MCP 服务调用
   - 对比两者的结果差异

4. 🔍 可能的问题
   - 服务没有完全重启
   - 代码修改没有保存
   - 缓存问题
   - 后端 RAGFlow 服务的重排序逻辑覆盖了参数

5. 📊 验证方法
   - 在 retrieval 函数中添加 print 语句打印参数
   - 在 call_tool 函数中添加 print 语句打印传入的 arguments
   - 观察控制台输出

6. 🛠️ 临时调试代码
   在 server.py 的 retrieval 函数开头添加:
   
   print(f"DEBUG: retrieval called with page_size={page_size}, top_k={top_k}")
   
   在 call_tool 函数开头添加:
   
   print(f"DEBUG: call_tool arguments={arguments}")
"""
    
    print(instructions)


if __name__ == "__main__":
    print("🔧 RAGFlow MCP 实时诊断工具")
    print("=" * 60)
    print("⚠️  请先修改脚本中的 API_KEY 和 dataset_ids 为实际值")
    print("=" * 60)
    
    # 检查服务状态
    check_service_status()
    
    # 测试 RAGFlow API
    api_ok = test_ragflow_api_directly()
    
    # 测试 MCP 服务
    if api_ok:
        mcp_ok = test_mcp_service_directly()
    
    # 生成调试说明
    generate_debug_instructions()
    
    print("\n" + "=" * 60)
    print("📋 诊断完成")
    print("=" * 60)
